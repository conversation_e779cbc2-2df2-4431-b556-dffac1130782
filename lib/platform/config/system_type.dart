enum SystemType { diy, epc, igen, mgrn }

/// 运行模式
// "0 无效
enum OperationMode {
// 2 自发自用模式(阳台光伏模式)
  spontaneousUse(2),
// 3 电池优先模式(备电模式）
  standbyMode(3),
// 5 分时用电
  tou(5),

  /// 10 智能模式 动态电价
  smartMode(7);

  final int value;

  const OperationMode(this.value);

  static OperationMode fromInt(int? value) {
    return OperationMode.values.firstWhere((e) => e.value == value,
        orElse: () => OperationMode.standbyMode);
  }

  int toInt() {
    return value;
  }
}

enum DevicesType {
  diyPhotovoltaic, // diy 光伏
  diyOneMachine, // diy 一体机 运行设置 进 diy 系统详情的运行设置
  epcPhotovoltaic, // epc 光伏 没有运行设置
  epcHybridInverter, // epc 混合逆变器 运行设置进 epc 的系统详情运行设置
  epcSmartBox, // epc smartbox 运行设置进 TODO 新的都是开关的
  epcBattery, // epc 电池 没有运行设置
  epcChargingPile, // epc 充电桩 TODO 进设置自动定时充电
  igenPhotovoltaic, // 英臻 光伏 详情没有设计稿
  igenInverter, // 英臻 逆变器 没有运行设置
  igenBattery, // 英臻 电池 详情没有设计稿
  igenCollectionRod, // 英臻 采集棒 没有运行设置
  accessoriesSmartSocket, // 辅件 智能插座
  accessoriesAirSwitch, // 辅件 空气开关
  accessoriesCT, // 辅件 CT
  accessoriesSmoke, // 辅件 烟感
  accessoriesFlood, // 辅件 水浸
}

// 蓝牙设备返回的设备类型
enum DeviceComponent {
  None,
  EMS, // 1	EMS
  BMS, // 2	BMS
  PCS, // 3	PCS(交流负载)
  PV, // 4	PV逆变器
  Electricity, // 5	电表
  Socket, // 6	插座
  CT, // 7	CT
  AirSwitch, // 8	空开
  AcStation, // 9	交流充电桩
  DcStation, // 10	直流充电桩
  DcBoard, // 11	DC板(直流负载)
  OilEngine, // 12	油机
  EPS, // 13	EPS
  BS, // 14	电池堆（BS）
  Defult, // 站位
  Defult1, // 站位
  Defult2, // 站位
  Defult3, // 站位
  Defult4, // 站位
  Htg, // 20 温湿度传感器
  Somke, // 21 烟感
  Flood, // 22 水浸
  ecoTracker, // 23 EcoTracker设备
  //
}
