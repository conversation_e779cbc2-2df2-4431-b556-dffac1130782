import 'dart:async';
import 'dart:convert';

import 'package:flutter_basic/event/start_local_mode_event.dart';
import 'package:flutter_basic/platform/bluetooth/local_mode_manger.dart';
import 'package:flutter_basic/platform/bluetooth/stream_utils.dart';
import 'package:flutter_basic/platform/community/receiver_manager.dart';
import 'package:flutter_basic/platform/mqtt/mqtt_tool.dart';
import 'package:flutter_basic/platform/utils/event_bus_manager.dart';
import 'package:flutter_basic/platform/utils/global.dart';
import 'package:flutter_basic/repositories/mqtt_repository/model/mqtt_config_response.dart';
import 'package:mqtt_client/mqtt_client.dart';
import 'package:rxdart/rxdart.dart';

import '../../config.dart';
import '../../features/system_detail/config/SystemConfig.dart';
import '../../repositories/monitor_repository/model/monitor_response_model.dart';
import '../../router/main_feature_routes/main_routes_type.dart';
import '../community/local_mode_message_manager.dart';
import '../community/monitor/utils.dart';
import '../config/system_type.dart';
import '../platform.dart';
import '../utils/aes_utils.dart';

class MqttDevicesConfig {
  // static String PCS = 'HB-PCS';
  static const String publishTopic = 'v1/iot_gw/cloud/geneverse/';

  static const String publishSourceTopic = 'v1/iot_gw/cloud/';
  static const String subscriptTopic = 'v1/iot_gw/gw/geneverse/';

  static const String subscriptDeadTopic = 'v1/iot_gw/gw_lwt/';

  static const String updateTopic = 'v1/iot_gw/gw/device/';

  static const String subscriptDataTopic = 'v1/iot_gw/gw/data/';
  static const String subscriptSourceDataTopic = 'v1/iot_gw/gw/';

  // 并机增加
  static const String publishClusterTopic = 'v1/iot_gw/cloud/data/';
  // 召唤设备列表
  static const String publishDeviceTopic = 'v1/iot_gw/cloud/device/';
}

typedef onMqttMessageReceivedForPowerGraph = void Function(
    MonitorResponseModel model);

class MqttManager {
  static MqttManager? _instance;

  MqttManager._internal() {
    _instance = this;
    _isOnline.listen((p0) {
      var isLocalMode = LocalModeManger.instance.isLocalModeNow;
      if (isOnlineNow && isLocalMode) {
        /// 自动连接云端
        logger.d('设备在线，自动退出本地模式');
        LocalModeManger.instance.close();
      } else if (!isOnlineNow && !isLocalMode) {
        /// 自动连接本地模式
        var currentSystemType =
            SystemConfig.getSystemType(SystemConfig.getCurrentSystemSeries());
        if (currentSystemType == SystemType.diy) {
          logger.i('设备离线，自动连接本地模式');
          EventBusManager.eventBus.fire(StartLocalModeEvent());
        }
      }
    });
  }

  ///设备在线与否状态
  final _isOnline = StreamControllerReEmit<bool>(initialValue: true);

  Stream<bool> get isOnline => _isOnline.stream;

  bool get isOnlineNow => _isOnline.latestValue;

  static MqttManager get instance => _instance ?? MqttManager._internal();

  CustomMqtt mqtt = CustomMqtt.getInstance();

  String gw_sn = '';
  String deviceName = '';
  String username = 'geneverse';
  String password = '';
  Timer? _timer;
  final ReceiverManager _receiverManager = ReceiverManager.instance;

  String getTopicUrl(String username) {
    return MqttDevicesConfig.subscriptTopic.replaceAll('geneverse', username);
  }

  String getDiyPublishUrl(String username) {
    return MqttDevicesConfig.publishTopic.replaceAll('geneverse', username);
  }

  void connectToServer(
    MqttConfigResponse mqttConfig, {
    ConnectCallback? onConnected,
    onMqttMessageReceivedForPowerGraph? onReceive,
  }) {
    username = mqttConfig.mqttUserName!;
    password = mqttConfig.mqttPassword!;
    String decryptPassword = EncryptUtils.getDecryptData(password);
    log('decryptPassword : $decryptPassword');

    CustomMqtt.getInstance().connect(
      mqttConfig.mqttServer!,
      int.parse(mqttConfig.mqttPort!),
      'mqtt_${DateTime.now().millisecondsSinceEpoch}',
      username,
      decryptPassword,
      onSubscribed: (topic) {},
      onConnected: () {
        logger.i('mqtt ------> 连接成功');
        onConnected?.call();
      },
      onDisconnected: () {
        _receiverManager.onDisConnected(onReceive: onReceive);
      },
      onReceive: (topic, message, c) {},
    );
  }

  void connectToServerBindMonitor(
    MqttConfigResponse mqttConfig,
    MonitorResponseModel? oldModel, {
    ConnectCallback? onConnected,
    onMqttMessageReceivedForPowerGraph? onReceive,
  }) {
    username = mqttConfig.mqttUserName!;
    password = mqttConfig.mqttPassword!;
    _receiverManager.cachedOldModel = oldModel;

    String decryptPassword = EncryptUtils.getDecryptData(password);
    log('decryptPassword : $decryptPassword');
    CustomMqtt.getInstance().connect(
      mqttConfig.mqttServer!,
      int.parse(mqttConfig.mqttPort!),
      'mqtt_${DateTime.now().millisecondsSinceEpoch}',
      username,
      decryptPassword,
      onSubscribed: (topic) {
        diyDeviceGet();
        log('mqtt ------> 订阅成功 : $topic');
        createTimer();
      },
      onConnected: () {
        onConnected?.call();
        // CustomMqtt.getInstance().subscribeMessage(CustomMqtt.subscriptTopic, keep: true);
      },
      onDisconnected: () {
        _receiverManager.onDisConnected(onReceive: onReceive);
      },
      onReceive: (topic, message, c) {
        SystemConfig.singleton.hasNet = true;
        if (showMqttLog) {
          logger.d(
              'mqtt ------> receive message onReceive $topic message: $message c: $c');
        }
        // logger.d(
        //     'mqtt ------> receive message onReceive $topic message: $message c: $c');

        if (topic.contains(MqttDevicesConfig.subscriptDeadTopic)) {
          Map<String, dynamic> map = json.decode(message);
          if (map['info'] == 'offline') {
            _receiverManager.onDeviceOffline(onReceive: onReceive);
            _isOnline.add(false);
          } else if (map['info'] == 'online') {
            _isOnline.add(true);
          }
          return;
        } else if (topic.contains(MqttDevicesConfig.subscriptDataTopic)) {
          // 这里目前只处理alarm_report
          _receiverManager.onDeviceReportMessage(message, onReceive: onReceive);
        } else {
          // 只要收到消息就认定设备在线
          // mqtt在收到离线遗嘱消息以后还可能会收到alerm_report
          // 如果在外层，会同步设置设备在线状态造成异常
          _isOnline.add(true);
          _receiverManager.onReceiveMessage(message, onReceive: onReceive);
        }
      },
    );
  }

  /// 订阅主题
  Stream<MqttReceivedMessage<String>> subscribeTopic(String topic) {
    mqtt.subscribeMessage(topic, keep: true);

    return mqtt.mqttClient.updates!
        .where((event) => event[0].topic == topic)
        .asyncMap((event) {
      final MqttPublishMessage message = event[0].payload as MqttPublishMessage;
      final payload =
          MqttPublishPayload.bytesToStringAsString(message.payload.message);
      return MqttReceivedMessage<String>(topic, payload);
    }).doOnCancel(() {
      mqtt.unsubscribeMessage(topic);
    });
  }

  void subscribeTopics() {
    logger.i(
        'subscribeTopics to topic: $MqttDevicesConfig.subscriptTopic gw_sn: $gw_sn');
    mqtt.subscribeMessage(getTopicUrl(username) + gw_sn, keep: true);
    mqtt.subscribeMessage(MqttDevicesConfig.subscriptDeadTopic + gw_sn,
        keep: true);
    mqtt.subscribeMessage(MqttDevicesConfig.subscriptDataTopic + gw_sn,
        keep: true);
  }

  // 并机新增
  // 订阅 DataTopic
  void subTopic(List<String> snList) {
    String dataTopic = MqttDevicesConfig.subscriptSourceDataTopic +
        MqttManager.instance.username +
        '/';
    snList.forEach((sn) {
      mqtt.subscribeMessage(dataTopic + sn, keep: true);
    });
  }

  // 并机新增
  // 取消订阅 DataTopic
  void unSubTopic(List<String> snList) {
    String dataTopic = MqttDevicesConfig.subscriptSourceDataTopic +
        MqttManager.instance.username +
        '/';
    snList.forEach((sn) {
      mqtt.unsubscribeMessage(dataTopic + sn);
    });
  }

  void diyDeviceGet() {
    if (LocalModeManger.instance.isLocalModeNow) {
      return;
    }
    log('mqtt ------> 定时器执行 每秒获取一次能流图数据');
    // 有dialog出现的时候也不会发消息 比如升级弹框
    if (Global.routeObserver.historyRoutes.isNotEmpty) {
      var currentRouterName = Global
          .routeObserver
          .historyRoutes[Global.routeObserver.historyRoutes.length - 1]
          .settings
          .name;
      // 如果不再白名单直接return
      if (!LocalModeMessageManager.whiteList.contains(currentRouterName)) {
        return;
      } else if ((MainRoutesType.rootRoute == currentRouterName ||
              MainRoutesType.mainRoute == currentRouterName) &&
          !LocalModeMessageManager.instance.isNeedSendNow) {
        // 如果在白名单内，再看MonitorPage是都显示 这个需要根据生命周期来判断
        return;
      }
    }
    List<Map<String, dynamic>> devList = [];
    for (var element in _receiverManager.keepDevices.keys) {
      var meterList = getPointsByProductKey(element);
      var devices = _receiverManager.keepDevices[element];
      devices?.forEach((device) {
        if (meterList.isNotEmpty) {
          devList.add({'dev_sn': device, 'meter_list': meterList});
        }
      });
    }
    Map<String, dynamic> params = {"dev_list": devList};
    publishMessage(
        'data_get', gw_sn, params, getDiyPublishUrl(username) + gw_sn);
  }

  void publishMessage(
      String cmd, String gw_sn, dynamic info, String publishTopic) {
    Map<String, dynamic> paramMap = {};
    paramMap["token"] = DateTime.now().millisecondsSinceEpoch;
    paramMap["cmd"] = cmd;
    paramMap["gw_sn"] = gw_sn;
    paramMap["timestamp"] = DateTime.now().millisecondsSinceEpoch.toString();
    if (info == null) {
      paramMap["info"] = {};
    } else {
      paramMap["info"] = info;
    }
    String params = json.encode(paramMap);
    if (showMqttLog) {
      logger.d('send message to topic: $publishTopic msg: $params');
    }
    mqtt.publishMessage(publishTopic, params);
  }

  void cleanKeepTopics() {
    cancelTimer();
    mqtt.cleanKeepTopics();
  }

  void createTimer() {
    cancelTimer();

    /// 定时发送
    _timer = Timer.periodic(Duration(milliseconds: 5000), (timer) {
      ///定时任务
      diyDeviceGet();
    });
  }

  void cancelTimer() {
    if (_timer != null && _timer!.isActive) {
      _timer?.cancel();
    }
    _timer = null;
  }

  void setIsOnline(bool flag) {
    _isOnline.add(flag);
  }

  log(String? msg) {
    if (CustomMqtt.debug) {
      logger.d("MQTT Manager-->$msg");
    }
  }

  clear() {
    cancelTimer();
    // 取消订阅
    mqtt.cleanKeepTopics();
    mqtt.unsubscribeMessage(MqttDevicesConfig.subscriptDataTopic + gw_sn);
    CustomMqtt.getInstance().disconnect();
  }
}
