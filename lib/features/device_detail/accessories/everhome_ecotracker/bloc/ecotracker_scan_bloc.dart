import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:flutter_basic/platform/utils/log_utils.dart';

import '../../../../../platform/system.dart';
import '../../../../../platform/config/system_type.dart';
import '../../../../../platform/bluetooth/blufi_manager.dart';
import '../../../../../repositories/diy_new_repository/diy_new_repository.dart';
import '../model/ecotracker_device_model.dart';
import '../service/mdns_scan_service.dart';
import 'ecotracker_scan_event.dart';
import 'ecotracker_scan_state.dart';

/// 内部事件：设备列表更新
class _DevicesUpdatedEvent extends EcoTrackerScanEvent {
  final List<EcoTrackerDeviceModel> devices;

  const _DevicesUpdatedEvent(this.devices);

  @override
  List<Object?> get props => [devices];
}

/// 内部事件：扫描定时器超时
class _ScanTimerExpiredEvent extends EcoTrackerScanEvent {
  const _ScanTimerExpiredEvent();
}

/// 内部事件：扫描定时器计时
class _ScanTimerTickEvent extends EcoTrackerScanEvent {
  final int remainingTime;

  const _ScanTimerTickEvent(this.remainingTime);

  @override
  List<Object?> get props => [remainingTime];
}

class EcoTrackerScanBloc
    extends Bloc<EcoTrackerScanEvent, EcoTrackerScanState> {
  final MdnsScanService _scanService = MdnsScanService();
  StreamSubscription<List<EcoTrackerDeviceModel>>? _devicesSubscription;
  Timer? _scanTimer;

  EcoTrackerScanBloc() : super(const EcoTrackerScanState()) {
    on<StartScanEvent>(_onStartScan);
    on<StopScanEvent>(_onStopScan);
    on<RefreshScanEvent>(_onRefreshScan);
    on<ConnectDeviceEvent>(_onConnectDevice);
    on<ClearScanResultsEvent>(_onClearScanResults);
    on<_DevicesUpdatedEvent>(_onDevicesUpdated);
    on<_ScanTimerExpiredEvent>(_onScanTimerExpired);
    on<_ScanTimerTickEvent>(_onScanTimerTick);

    // 监听设备发现
    _devicesSubscription = _scanService.devicesStream.listen((devices) {
      if (!isClosed) {
        add(_DevicesUpdatedEvent(devices));
      }
    });
  }

  /// 处理设备列表更新
  Future<void> _onDevicesUpdated(
      _DevicesUpdatedEvent event, Emitter<EcoTrackerScanState> emit) async {
    logger.d('设备列表更新: 发现 ${event.devices.length} 个设备');
    for (var device in event.devices) {
      logger.d('设备详情: ${device.toString()}');
    }
    emit(state.copyWith(devices: event.devices));
  }

  /// 处理扫描定时器超时
  Future<void> _onScanTimerExpired(
      _ScanTimerExpiredEvent event, Emitter<EcoTrackerScanState> emit) async {
    emit(state.copyWith(
      scanStatus: ScanStatus.success,
      remainingTime: 0,
    ));
  }

  /// 处理扫描定时器计时
  Future<void> _onScanTimerTick(
      _ScanTimerTickEvent event, Emitter<EcoTrackerScanState> emit) async {
    emit(state.copyWith(remainingTime: event.remainingTime));
  }

  /// 开始扫描设备
  Future<void> _onStartScan(
      StartScanEvent event, Emitter<EcoTrackerScanState> emit) async {
    try {
      logger.d('开始扫描 EcoTracker 设备');

      // 清空之前的设备列表
      _scanService.clearDevices();

      emit(state.copyWith(
        scanStatus: ScanStatus.scanning,
        devices: [], // 清空设备列表
        error: null,
        remainingTime: state.scanDuration,
      ));

      // 启动扫描服务
      await _scanService.startScan(
        timeout: Duration(seconds: state.scanDuration),
      );

      // 启动倒计时定时器
      _startScanTimer(emit);
    } catch (e) {
      logger.e('启动扫描失败: $e');
      emit(state.copyWith(
        scanStatus: ScanStatus.failure,
        error: '启动扫描失败: $e',
      ));
    }
  }

  /// 停止扫描设备
  Future<void> _onStopScan(
      StopScanEvent event, Emitter<EcoTrackerScanState> emit) async {
    try {
      logger.d('停止扫描 EcoTracker 设备');

      _scanTimer?.cancel();
      _scanTimer = null;

      await _scanService.stopScan();

      emit(state.copyWith(
        scanStatus: ScanStatus.stopped,
        remainingTime: 0,
      ));
    } catch (e) {
      logger.e('停止扫描失败: $e');
      emit(state.copyWith(
        scanStatus: ScanStatus.failure,
        error: '停止扫描失败: $e',
      ));
    }
  }

  /// 刷新扫描
  Future<void> _onRefreshScan(
      RefreshScanEvent event, Emitter<EcoTrackerScanState> emit) async {
    // 清空当前结果
    _scanService.clearDevices();
    emit(state.copyWith(devices: []));

    // 重新开始扫描
    add(const StartScanEvent());
  }

  /// 连接设备
  Future<void> _onConnectDevice(
      ConnectDeviceEvent event, Emitter<EcoTrackerScanState> emit) async {
    try {
      logger.d('连接设备: ${event.deviceName} (${event.ipAddress}:${event.port})');

      emit(state.copyWith(
        connectStatus: ConnectStatus.connecting,
        error: null,
      ));

      await _connectToDevice(event);

      emit(state.copyWith(
        connectStatus: ConnectStatus.connected,
        connectedDeviceId: event.deviceId,
      ));
    } catch (e) {
      logger.e('连接设备失败: $e');
      emit(state.copyWith(
        connectStatus: ConnectStatus.failed,
        error: '连接设备失败: $e',
      ));
    }
  }

  /// 清空扫描结果
  Future<void> _onClearScanResults(
      ClearScanResultsEvent event, Emitter<EcoTrackerScanState> emit) async {
    _scanService.clearDevices();
    emit(state.copyWith(
      devices: [],
      scanStatus: ScanStatus.initial,
      connectStatus: ConnectStatus.initial,
      connectedDeviceId: null,
      error: null,
    ));
  }

  /// 启动扫描倒计时定时器
  void _startScanTimer(Emitter<EcoTrackerScanState> emit) {
    _scanTimer?.cancel();

    _scanTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (isClosed) {
        timer.cancel();
        return;
      }

      // 使用 add 方法而不是直接 emit，避免在事件处理器外部调用 emit
      final remainingTime = state.remainingTime - 1;

      if (remainingTime <= 0) {
        timer.cancel();
        add(const _ScanTimerExpiredEvent());
        _scanService.stopScan();
      } else {
        add(_ScanTimerTickEvent(remainingTime));
      }
    });
  }

  /// 添加设备
  Future<void> _connectToDevice(ConnectDeviceEvent event) async {
    try {
      // 获取当前系统ID
      final systemId = getSystemId();
      if (systemId.isEmpty) {
        throw Exception('系统ID未找到');
      }

      logger.d('开始添加EcoTracker设备: ${event.deviceName} (${event.deviceId})');

      // 1. 发送smart_add命令添加设备
      await _sendSmartAddCommand(event.deviceId, systemId);

      // 2. 查找新设备，30秒超时
      await _findNewDevice(event.deviceId, event.deviceName, systemId);

      logger.d('EcoTracker设备添加成功: ${event.deviceName}');
    } catch (e) {
      logger.e('添加EcoTracker设备失败: $e');
      rethrow;
    }
  }

  /// 发送smart_add命令
  Future<void> _sendSmartAddCommand(String deviceId, String systemId) async {
    try {
      // 通过蓝牙发送smart_add命令
      await BlufiManager.instance.addSmartDevice(
        shelly_sn: deviceId,
        deviceType: DeviceComponent.ecoTracker, // EcoTracker设备类型
        linkPara: "", // EcoTracker可能需要特定的连接参数
      );

      logger.d('smart_add命令发送成功: $deviceId');
    } catch (e) {
      logger.e('发送smart_add命令失败: $e');
      throw Exception('发送smart_add命令失败: $e');
    }
  }

  @override
  Future<void> close() {
    _scanTimer?.cancel();
    _devicesSubscription?.cancel();
    _scanService.dispose();
    return super.close();
  }

  /// 查找新设备
  Future<void> _findNewDevice(
      String deviceId, String deviceName, String systemId) async {
    // 30秒超时
    var startTime = DateTime.now();
    deviceId = deviceId.toUpperCase();

    logger.d('开始查找新设备: $deviceName ($deviceId)');

    while (true) {
      try {
        // 获取设备信息
        var data = await BlufiManager.instance.getDeviceInfo();
        var deviceList = data.dev_list
            ?.where((e) => e.dev_sn.toUpperCase() == deviceId)
            .toList();
        var device = deviceList?.isNotEmpty == true ? deviceList!.first : null;

        // 判断设备存在，且通讯正常
        if (device != null && device.status == "0") {
          // 将device_get数据上报到云端，避免设备未上报导致设备列表不显示
          await DiyNewClientRepository.instance.deviceReport(systemId, data);

          // 上报EcoTracker设备数量
          var ecoTrackerIndex = DeviceComponent.ecoTracker.index;
          var count = data.dev_list
                  ?.where((e) => int.parse(e.dev_type) == ecoTrackerIndex)
                  .length ??
              0;
          await DiyNewClientRepository.instance
              .devicePropertyReport('ems_$systemId', [
            {'name': 'ecoTrackers', 'value': count}
          ]);

          logger.d('查找新设备成功: $deviceName ($deviceId)');
          return;
        }

        // 检查是否超时（30秒）
        if (DateTime.now().difference(startTime).inSeconds >= 30) {
          logger.e('[add_smart] 未找到新设备信息，超时');
          throw Exception('未找到新设备信息，超时');
        }

        logger.d('[add_smart] 未找到新设备信息，1秒后重试');
        await Future.delayed(const Duration(seconds: 1));
      } catch (e) {
        // 如果是超时异常，直接抛出
        if (e.toString().contains('超时')) {
          rethrow;
        }

        // 其他异常，检查是否超时
        if (DateTime.now().difference(startTime).inSeconds >= 30) {
          logger.e('[add_smart] 查找设备过程中发生错误且超时: $e');
          throw Exception('查找设备超时: $e');
        }

        logger.w('[add_smart] 查找设备过程中发生错误，1秒后重试: $e');
        await Future.delayed(const Duration(seconds: 1));
      }
    }
  }
}
