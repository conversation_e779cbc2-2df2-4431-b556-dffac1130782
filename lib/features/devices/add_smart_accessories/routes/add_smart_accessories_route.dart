import 'package:flutter_basic/platform/platform.dart';

import '../page/add_smart_accessories_page.dart';

class AddSmartAccessoriesRoute extends StatelessWidget {
  const AddSmartAccessoriesRoute({super.key});

  @override
  Widget build(BuildContext context) {
    dynamic arguments = ModalRoute.of(context)?.settings.arguments;
    String? systemId = arguments?['systemId'];
    String? systemNo = arguments?['systemNo'];
    bool clustered = arguments?['clustered'] ?? false;

    return AddSmartAccessoriesPage(
      systemId: systemId,
      systemNo: systemNo,
      clustered: clustered,
    );
  }
}
